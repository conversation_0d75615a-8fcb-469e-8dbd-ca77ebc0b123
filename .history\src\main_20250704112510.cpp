#include <WiFi.h>
#include <PubSubClient.h>
#include <SimpleDHT.h>

// 引脚定义
#define LED_PIN 5
#define DHTPIN 15
SimpleDHT11 dht(DHTPIN);

// 函数原型声明
void connectMQTT();

// WiFi
#define WLAN_SSID "FBI-NETWORK"
#define WLAN_PASS "13926095770"

// MQTT
const char *mqttBroker = "192.168.137.103";
const int mqttPort = 1883;
const char *mqttUsername = "zigbee";
const char *mqttPassword = "zigbee";

// MQTT topic
const char *ledTopic = "LOLIN_D32_HOME/DHT11-LED";
const char *tempTopic = "LOLIN_D32_HOME/DHT11-temperature";
const char *humidTopic = "LOLIN_D32_HOME/DHT11-humidity";
const char *availabilityTopic = "LOLIN_D32_HOME/DHT11-status";

WiFiClient espClient;
PubSubClient mqttClient(espClient);

// LED 回调
void callback(char* topic, byte* payload, unsigned int length) {
  String message;
  for (int i = 0; i < length; i++) {
    message += (char)payload[i];
  }

  if (String(topic) == ledTopic) {
    if (message == "1") {
      digitalWrite(LED_PIN, HIGH);  // 熄灭
      Serial.println("LED is now OFF");
    } else if (message == "0") {
      digitalWrite(LED_PIN, LOW);   // 点亮
      Serial.println("LED is now ON");
    }
    // 发布LED的状态（同步到HA）
    mqttClient.publish("LOLIN_D32_HOME/DHT11-LED-state", message.c_str(), true); // 状态同步
  }
}

void setup() {
  Serial.begin(115200);
  delay(10);

  pinMode(LED_PIN, OUTPUT);
  digitalWrite(LED_PIN, LOW);

  Serial.println("Connecting to WiFi...");
  WiFi.begin(WLAN_SSID, WLAN_PASS);
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println("\nWiFi connected");
  Serial.print("IP address: ");
  Serial.println(WiFi.localIP());

  mqttClient.setServer(mqttBroker, mqttPort);
  mqttClient.setCallback(callback);
  
  connectMQTT();
}

void loop() {
  if (!mqttClient.connected()) {
    connectMQTT();
  }
  
  mqttClient.loop();

  // 读DHT11
  float DHT11temperature = 0;
  float DHT11humidity = 0;
  if (dht.read2(&DHT11temperature, &DHT11humidity, NULL) == SimpleDHTErrSuccess) {
    char tempStr[20];
    char humidStr[20];
    snprintf(tempStr, sizeof(tempStr), "%.2f", DHT11temperature);
    snprintf(humidStr, sizeof(humidStr), "%.2f", DHT11humidity);

    mqttClient.publish(tempTopic, tempStr, true);  // retain
    mqttClient.publish(humidTopic, humidStr, true);

    Serial.print("Temperature: "); Serial.println(tempStr);
    Serial.print("Humidity: "); Serial.println(humidStr);
  } else {
    Serial.println("DHT11 read error.");
  }

  delay(2000);
}

void connectMQTT() {
  while (!mqttClient.connected()) {
    String clientId = "ESP32Client-";
    clientId += String(random(0xffff), HEX);

    Serial.print("Connecting to MQTT...");

    // connect 参数:
    // clientID, username, password, lwt topic, qos, retain, lwt message
    if (mqttClient.connect(clientId.c_str(),
                           mqttUsername,
                           mqttPassword,
                           availabilityTopic,
                           1,           // qos
                           true,        // true：retain on（断电之后，MQTT 中还是 online）；false: retain off
                           "offline"))  // lwt payload
    {
      Serial.println("connected");

      // 订阅 LED 控制
      mqttClient.subscribe(ledTopic);

      // 上电主动发布在线状态
      mqttClient.publish(availabilityTopic, "online", true);
    } else {
      Serial.print("failed, rc=");
      Serial.print(mqttClient.state());
      Serial.println(" retrying in 5 seconds");
      delay(5000);
    }
  }
}