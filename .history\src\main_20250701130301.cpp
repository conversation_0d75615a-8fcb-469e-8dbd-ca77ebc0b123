#include <WiFi.h>
#include <PubSubClient.h>
#include <SimpleDHT.h>

// #define soil1_moisture_pin A0
// #define soil2_moisture_pin A3
#define LED_PIN 5           // 定义LED连接的5数字引脚
#define DHTPIN 15          // 定义DHT连接的15数字引脚
#define DHTTYPE DHT22     // 定义DHT类型为DHT22
SimpleDHT22 dht(DHTPIN);
/************************* WiFi Access Point *********************************/

//#define WLAN_SSID "BEEPLUS GUEST"
//#define WLAN_PASS "todaybeeplus"

// #define WLAN_SSID "NCON-TURBO-S"
// #define WLAN_PASS "lxl13926095770"

#define WLAN_SSID "FBI-NETWORK"
#define WLAN_PASS "13926095770"

/************************* Mosquitto broker MQTT Setting *********************************/
const char *mqttBroker = "192.168.137.103";
const int mqttPort = 1883;
const char *mqttUsername = "zigbee";
const char *mqttPassword = "zigbee";

// MQTT 主题
const char *ledTopic = "LOLIN_D32_HOME/LED1";
const char *tempTopic = "LOLIN_D32_HOME/DHT22-temperature";
const char *humidTopic = "LOLIN_D32_HOME/DHT22-humidity";

WiFiClient espClient;
PubSubClient mqttClient(espClient);

// LED部分，MQTT控制回调函数
void callback(char* topic, byte* payload, unsigned int length) {
    // 转换 payload 为字符串
    String message;
    for (int i = 0; i < length; i++) {
        message += (char)payload[i];
    }

    // 检查主题并处理消息
    if (String(topic) == ledTopic) {
        if (message == "1") {
            digitalWrite(LED_PIN, HIGH); // 熄灭 LED
            Serial.println("LED is now OFF");
        } else if (message == "0") {
            digitalWrite(LED_PIN, LOW); // 点亮 LED
            Serial.println("LED is now ON");
        }
    }
}

void setup() {
  Serial.begin(115200);
  delay(10);

  /*******************************LED部分-setup**********************************/
  pinMode(LED_PIN, OUTPUT); // 将LED引脚设置为输出
  digitalWrite(LED_PIN, LOW); // 初始状态开启LED

  Serial.println("Connecting to WiFi...");
  WiFi.begin(WLAN_SSID, WLAN_PASS);
  while (WiFi.status() != WL_CONNECTED) {
      delay(500);
      Serial.print(".");
  }
  Serial.println("\nWiFi connected");
  Serial.print("IP address: ");
  Serial.println(WiFi.localIP());

  // LED部分
  mqttClient.setServer(mqttBroker, mqttPort);
  mqttClient.setCallback(callback);
  MQTT_connect();

}

void loop() {
    if (!mqttClient.connected()) {
        MQTT_connect();
    }
    mqttClient.loop();

    // DHT22 Temperature and Humidity Reading
    float DHT22temperature = 0;
    float DHT22humidity = 0;
    int err = SimpleDHTErrSuccess;
    if ((err = dht.read2(&DHT22temperature, &DHT22humidity, NULL)) != SimpleDHTErrSuccess) {
        Serial.print("Read DHT22 failed, err="); Serial.println(err);
        delay(1000); // 等待1秒再次尝试
        return;
    }

    // 创建存储温度和湿度字符串的缓冲区
    char tempStr[20];
    char humidStr[20];

    // 格式化温度和湿度数据
    snprintf(tempStr, sizeof(tempStr), "%.2f", DHT22temperature);
    snprintf(humidStr, sizeof(humidStr), "%.2f", DHT22humidity);

    // 发布温度和湿度数据
    mqttClient.publish(tempTopic, tempStr);
    mqttClient.publish(humidTopic, humidStr);
    //Client.publish(tempTopic);
    //Client.publish(humidTopic);

    // 打印到串口
    Serial.print("Temperature: "); Serial.println(tempStr);
    Serial.print("Humidity: "); Serial.println(humidStr);

    // 为了防止过于频繁的发布数据，可以在此处添加延时
    delay(2000); // 例如，每2秒发布一次数据
}


// Function to connect and reconnect as necessary to the MQTT server.
// Should be called in the loop function and it will take care if connecting.
void MQTT_connect() {
  while (!mqttClient.connected()) {
    String clientId = "ESP32Client-";
    clientId += String(random(0xffff), HEX);
    Serial.print("EMQX MQTT connection...");
    if (mqttClient.connect(clientId.c_str(), mqttUsername, mqttPassword)) {
      Serial.println("connected");
      // 订阅所有相关主题
      mqttClient.subscribe(ledTopic);
      mqttClient.subscribe(tempTopic);
      mqttClient.subscribe(humidTopic);
    } else {
      Serial.print("failed, rc=");
      Serial.print(mqttClient.state());
      Serial.println(" try again in 5 seconds");
      delay(5000);
    }
  }
}
