- sensor: 
    - name: ESP32-温度
      unique_id: "ESP32-Temperature"
      state_topic: "LOLIN_D32_HOME/DHT22-temperature"
      unit_of_measurement: "°C"
      
    - name: ESP32-湿度
      unique_id: "ESP32-Humidity"
      state_topic: "LOLIN_D32_HOME/DHT22-humidity"
      unit_of_measurement: "%"
      
    # 新增DHT11设备配置
    - name: "ESP32-DHT11-温度"
      unique_id: "esp32_dht11_temperature_sensor"
      state_topic: "LOLIN_D32_HOME/DHT11-temperature"
      unit_of_measurement: "°C"
      availability_topic: "LOLIN_D32_HOME/status"
      payload_available: "online"
      payload_not_available: "offline"
      qos: 1
    
    - name: "ESP32-DHT11-湿度"
      unique_id: "esp32_dht11_humidity_sensor"
      state_topic: "LOLIN_D32_HOME/DHT11-humidity"
      unit_of_measurement: "%"
      availability_topic: "LOLIN_D32_HOME/status"
      payload_available: "online"
      payload_not_available: "offline"
      qos: 1

- switch:
    - name: "ESP32-LED"
      unique_id: "esp32_led_switch"
      command_topic: "LOLIN_D32_HOME/LED1"
      state_topic: "LOLIN_D32_HOME/LED1"
      availability_topic: "LOLIN_D32_HOME/status"
      payload_available: "online"
      payload_not_available: "offline"
      payload_on: "0"  # 注意：您的代码中0是开灯，1是关灯
      payload_off: "1"
      qos: 1
