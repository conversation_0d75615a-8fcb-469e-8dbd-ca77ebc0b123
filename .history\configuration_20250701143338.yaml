# MQTT设备配置
switch:
  - platform: mqtt
    name: "<PERSON><PERSON>32 LED"
    command_topic: "LOLIN_D32_HOME/LED1"
    state_topic: "LOLIN_D32_HOME/LED1"
    availability_topic: "LOLIN_D32_HOME/status"
    payload_available: "online"
    payload_not_available: "offline"
    payload_on: "0"  # 注意：您的代码中0是开灯，1是关灯
    payload_off: "1"
    qos: 1
    unique_id: esp32_led_switch

sensor:
  - platform: mqtt
    name: "ESP32 Temperature"
    state_topic: "LOLIN_D32_HOME/DHT11-temperature"
    unit_of_measurement: "°C"
    availability_topic: "LOLIN_D32_HOME/status"
    payload_available: "online"
    payload_not_available: "offline"
    qos: 1
    unique_id: esp32_temperature_sensor
    
  - platform: mqtt
    name: "ESP32 Humidity"
    state_topic: "LOLIN_D32_HOME/DHT11-humidity"
    unit_of_measurement: "%"
    availability_topic: "LOLIN_D32_HOME/status"
    payload_available: "online"
    payload_not_available: "offline"
    qos: 1
    unique_id: esp32_humidity_sensor