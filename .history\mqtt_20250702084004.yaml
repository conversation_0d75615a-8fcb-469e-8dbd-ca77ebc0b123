- sensor: 
    - name: ESP32-温度
      unique_id: "ESP32-Temperature"
      state_topic: "LOLIN_D32_HOME/DHT22-temperature"
      unit_of_measurement: "°C"
      availability:
        - topic: "LOLIN_D32_HOME/DHT22-temperature"
          payload_available: ""
          payload_not_available: ""
          value_template: "{{ value_json is defined }}"
      availability_mode: all
      availability_timeout: 300  # 5分钟超时
      
    - name: ESP32-湿度
      unique_id: "ESP32-Humidity"
      state_topic: "LOLIN_D32_HOME/DHT22-humidity"
      unit_of_measurement: "%"
      availability:
        - topic: "LOLIN_D32_HOME/DHT22-humidity"
          payload_available: ""
          payload_not_available: ""
          value_template: "{{ value_json is defined }}"
      availability_mode: all
      availability_timeout: 300  # 5分钟超时

